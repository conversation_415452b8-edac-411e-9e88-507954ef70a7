package com.alpha.gallery.navigation

import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.core.ui.permission.*
import com.alpha.gallery.permission.PermissionCoordinator
import com.alpha.gallery.feature.gallery.GalleryScreen
import com.alpha.gallery.feature.mediaviewer.MediaViewerScreenWithPermissionGate
import com.alpha.gallery.feature.albums.AlbumsScreen
import com.alpha.gallery.feature.cloud.CloudScreen
import com.alpha.gallery.feature.settings.SettingsScreen

/**
 * Main navigation destinations
 */
object GalleryDestinations {
    const val PERMISSION_SETUP = "permission_setup"
    const val GALLERY = "gallery"
    const val ALBUMS = "albums"
    const val MEDIA_VIEWER = "media_viewer"
    const val CLOUD = "cloud"
    const val SETTINGS = "settings"
}

/**
 * Main navigation component for the Gallery app
 */
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun GalleryNavigation(
    navController: NavHostController = rememberNavController(),
    permissionCoordinator: PermissionCoordinator,
    permissionManager: PermissionManager,
    sharedTransitionScope: SharedTransitionScope,
    startDestination: String = GalleryDestinations.GALLERY
) {
    val context = LocalContext.current
    
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Permission setup screen
        composable(GalleryDestinations.PERMISSION_SETUP) {
            PermissionSetupScreen(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator,
                onPermissionsGranted = {
                    navController.navigate(GalleryDestinations.GALLERY) {
                        popUpTo(GalleryDestinations.PERMISSION_SETUP) { inclusive = true }
                    }
                },
                onSkip = {
                    navController.navigate(GalleryDestinations.GALLERY) {
                        popUpTo(GalleryDestinations.PERMISSION_SETUP) { inclusive = true }
                    }
                }
            )
        }
        
        // Main gallery screen
        composable(GalleryDestinations.GALLERY) {
            GalleryScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator,
                onNavigateToPermissionSetup = {
                    navController.navigate(GalleryDestinations.PERMISSION_SETUP)
                },
                navController = navController,
                sharedTransitionScope = sharedTransitionScope,
                animatedVisibilityScope = this@composable
            )
        }
        
        // Albums screen
        composable(GalleryDestinations.ALBUMS) {
            AlbumsScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator
            )
        }
        
        // Media viewer screen
        composable(
            route = "${GalleryDestinations.MEDIA_VIEWER}/{itemId}",
            arguments = listOf(navArgument("itemId") { type = NavType.StringType })
        ) { backStackEntry ->
            val itemId = backStackEntry.arguments?.getString("itemId") ?: ""
            MediaViewerScreenWithPermissionGate(
                startingItemId = itemId,
                onNavigateBack = {
                    navController.popBackStack()
                },
                sharedTransitionScope = sharedTransitionScope,
                animatedVisibilityScope = this@composable
            )
        }
        
        // Cloud screen
        composable(GalleryDestinations.CLOUD) {
            CloudScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator
            )
        }
        
        // Settings screen
        composable(GalleryDestinations.SETTINGS) {
            SettingsScreenWithPermissionGate(
                permissionManager = permissionManager,
                permissionCoordinator = permissionCoordinator
            )
        }
    }
}

/**
 * Permission setup screen wrapper
 */
@Composable
private fun PermissionSetupScreen(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator,
    onPermissionsGranted: () -> Unit,
    onSkip: () -> Unit
) {
    val context = LocalContext.current
    var permissionGranted by remember { mutableStateOf(false) }

    // Trigger sync when permission is granted
    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
            onPermissionsGranted()
        }
    }

    val requestPermissions = rememberPermissionLauncher(
        permissionManager = permissionManager,
        onPermissionResult = { granted, permanentlyDenied ->
            if (granted) {
                permissionGranted = true
            }
        }
    )
    
    PermissionScreen(
        permissionManager = permissionManager,
        onRequestPermissions = requestPermissions,
        onOpenSettings = {
            context.startActivity(permissionManager.createAppSettingsIntent())
        },
        onSkip = onSkip,
        showSettingsOption = false
    )
}

/**
 * Gallery screen with permission gate
 */
@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
private fun GalleryScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator,
    onNavigateToPermissionSetup: () -> Unit,
    navController: NavHostController,
    sharedTransitionScope: SharedTransitionScope,
    animatedVisibilityScope: androidx.compose.animation.AnimatedVisibilityScope
) {
    var permissionGranted by remember { mutableStateOf(false) }

    // Trigger sync when permissions are granted
    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }

    PermissionGate(
        permissionManager = permissionManager,
        onPermissionGranted = {
            permissionGranted = true
        },
        showFullScreen = false,
        allowSkip = true
    ) {
        GalleryScreen(
            onNavigateToMediaViewer = { itemId ->
                navController.navigate("${GalleryDestinations.MEDIA_VIEWER}/$itemId")
            },
            onShowMessage = { message ->
                // TODO: Show snackbar or toast
            },
            onShowError = { error ->
                // TODO: Show error snackbar or toast
            },
            sharedTransitionScope = sharedTransitionScope,
            animatedVisibilityScope = animatedVisibilityScope
        )
    }
}

/**
 * Albums screen with permission gate
 */
@Composable
private fun AlbumsScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator
) {
    var permissionGranted by remember { mutableStateOf(false) }

    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }

    SimplePermissionGate(
        permissionManager = permissionManager,
        onPermissionGranted = {
            permissionGranted = true
        }
    ) {
        AlbumsScreen()
    }
}



/**
 * Cloud screen with permission gate
 */
@Composable
private fun CloudScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator
) {
    var permissionGranted by remember { mutableStateOf(false) }

    LaunchedEffect(permissionGranted) {
        if (permissionGranted) {
            permissionCoordinator.triggerPermissionCheckAndSync()
        }
    }

    SimplePermissionGate(
        permissionManager = permissionManager,
        onPermissionGranted = {
            permissionGranted = true
        }
    ) {
        CloudScreen()
    }
}

/**
 * Settings screen with permission gate
 */
@Composable
private fun SettingsScreenWithPermissionGate(
    permissionManager: PermissionManager,
    permissionCoordinator: PermissionCoordinator
) {
    // Settings screen might not need strict permission enforcement
    SettingsScreen()
}


