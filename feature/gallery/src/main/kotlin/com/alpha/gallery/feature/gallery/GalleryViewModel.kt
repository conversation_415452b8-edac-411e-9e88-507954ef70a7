package com.alpha.gallery.feature.gallery

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.feature.gallery.model.GalleryUiState
import com.alpha.gallery.feature.gallery.model.GalleryUiEvent
import com.alpha.gallery.feature.gallery.model.GalleryUiAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Gallery screen
 */
@HiltViewModel
class GalleryViewModel @Inject constructor(
    private val mediaRepository: MediaRepository,
    private val permissionManager: PermissionManager
) : ViewModel() {

    companion object {
        private const val TAG = "GalleryViewModel"
    }
    
    private val _uiState = MutableStateFlow(GalleryUiState())
    val uiState: StateFlow<GalleryUiState> = _uiState.asStateFlow()
    
    private val _uiActions = MutableSharedFlow<GalleryUiAction>(
        replay = 0,
        extraBufferCapacity = 1
    )
    val uiActions: SharedFlow<GalleryUiAction> = _uiActions.asSharedFlow()
    
    init {
        // Initialize the screen
        initializeGallery()
        
        // Observe media items
        observeMediaItems()
        
        // Check permissions periodically
        observePermissions()
    }
    
    /**
     * Handle UI events
     */
    fun onEvent(event: GalleryUiEvent) {
        Log.d(TAG, "onEvent called with: $event")
        when (event) {
            is GalleryUiEvent.Refresh -> refreshMediaItems()
            is GalleryUiEvent.RetryLoad -> retryLoad()
            is GalleryUiEvent.ClearError -> clearError()
            is GalleryUiEvent.ToggleSelectionMode -> toggleSelectionMode()
            is GalleryUiEvent.ClearSelection -> clearSelection()
            is GalleryUiEvent.SelectItem -> selectItem(event.itemId)
            is GalleryUiEvent.DeselectItem -> deselectItem(event.itemId)
            is GalleryUiEvent.OpenMediaItem -> openMediaItem(event.itemId)
            is GalleryUiEvent.ToggleFavorite -> toggleFavorite(event.itemId)
            is GalleryUiEvent.DeleteItems -> deleteItems(event.itemIds)
            is GalleryUiEvent.ChangeGridColumns -> changeGridColumns(event.columns)
        }
    }
    
    private fun initializeGallery() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null,
                    hasPermissions = permissionManager.hasMediaPermissions()
                )
                
                if (!permissionManager.hasMediaPermissions()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "Media permissions required to display gallery"
                    )
                    return@launch
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Failed to initialize gallery"
                )
            }
        }
    }
    
    private fun observeMediaItems() {
        viewModelScope.launch {
            mediaRepository.getAllMediaItems()
                .catch { e ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = e.message ?: "Failed to load media items"
                    )
                }
                .collect { mediaItems ->
                    _uiState.value = _uiState.value.copy(
                        mediaItems = mediaItems,
                        isLoading = false,
                        isRefreshing = false,
                        isEmpty = mediaItems.isEmpty(),
                        error = null
                    )
                }
        }
    }
    
    private fun observePermissions() {
        viewModelScope.launch {
            // Check permissions every few seconds
            while (true) {
                val hasPermissions = permissionManager.hasMediaPermissions()
                if (_uiState.value.hasPermissions != hasPermissions) {
                    _uiState.value = _uiState.value.copy(hasPermissions = hasPermissions)
                    
                    if (hasPermissions) {
                        // Permissions granted, retry loading
                        initializeGallery()
                    }
                }
                kotlinx.coroutines.delay(3000) // Check every 3 seconds
            }
        }
    }
    
    private fun refreshMediaItems() {
        if (!permissionManager.hasMediaPermissions()) {
            _uiActions.tryEmit(GalleryUiAction.ShowError("Media permissions required"))
            return
        }
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isRefreshing = true, error = null)
                mediaRepository.refreshMediaItems()
                _uiActions.tryEmit(GalleryUiAction.ShowMessage("Gallery refreshed"))
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isRefreshing = false,
                    error = e.message ?: "Failed to refresh gallery"
                )
            }
        }
    }
    
    private fun retryLoad() {
        initializeGallery()
    }
    
    private fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    private fun toggleSelectionMode() {
        val currentState = _uiState.value
        _uiState.value = currentState.copy(
            isSelectionMode = !currentState.isSelectionMode,
            selectedItems = if (!currentState.isSelectionMode) emptySet() else currentState.selectedItems
        )
    }
    
    private fun clearSelection() {
        _uiState.value = _uiState.value.copy(
            selectedItems = emptySet(),
            isSelectionMode = false
        )
    }
    
    private fun selectItem(itemId: String) {
        val currentSelected = _uiState.value.selectedItems
        _uiState.value = _uiState.value.copy(
            selectedItems = currentSelected + itemId,
            isSelectionMode = true
        )
    }
    
    private fun deselectItem(itemId: String) {
        val currentSelected = _uiState.value.selectedItems
        val newSelected = currentSelected - itemId
        _uiState.value = _uiState.value.copy(
            selectedItems = newSelected,
            isSelectionMode = newSelected.isNotEmpty()
        )
    }
    
    private fun openMediaItem(itemId: String) {
        Log.d(TAG, "openMediaItem called with itemId: $itemId")
        Log.d(TAG, "isSelectionMode: ${_uiState.value.isSelectionMode}")

        if (_uiState.value.isSelectionMode) {
            // In selection mode, select/deselect instead of opening
            if (_uiState.value.selectedItems.contains(itemId)) {
                deselectItem(itemId)
            } else {
                selectItem(itemId)
            }
        } else {
            // Normal mode, navigate to media viewer
            Log.d(TAG, "Emitting NavigateToMediaViewer action for itemId: $itemId")
            val emitResult = _uiActions.tryEmit(GalleryUiAction.NavigateToMediaViewer(itemId))
            Log.d(TAG, "Action emit result: $emitResult")
        }
    }
    
    private fun toggleFavorite(itemId: String) {
        viewModelScope.launch {
            try {
                val mediaItem = _uiState.value.mediaItems.find { it.id == itemId }
                if (mediaItem != null) {
                    if (mediaItem.isFavorite) {
                        mediaRepository.removeFromFavorites(itemId)
                        _uiActions.tryEmit(GalleryUiAction.ShowMessage("Removed from favorites"))
                    } else {
                        mediaRepository.addToFavorites(itemId)
                        _uiActions.tryEmit(GalleryUiAction.ShowMessage("Added to favorites"))
                    }
                }
            } catch (e: Exception) {
                _uiActions.tryEmit(GalleryUiAction.ShowError("Failed to update favorites"))
            }
        }
    }
    
    private fun deleteItems(itemIds: Set<String>) {
        viewModelScope.launch {
            try {
                var successCount = 0
                itemIds.forEach { itemId ->
                    if (mediaRepository.deleteMediaItem(itemId)) {
                        successCount++
                    }
                }
                
                clearSelection()
                
                if (successCount == itemIds.size) {
                    _uiActions.tryEmit(GalleryUiAction.ShowMessage("$successCount items deleted"))
                } else {
                    _uiActions.tryEmit(GalleryUiAction.ShowMessage("$successCount of ${itemIds.size} items deleted"))
                }
                
            } catch (e: Exception) {
                _uiActions.tryEmit(GalleryUiAction.ShowError("Failed to delete items"))
            }
        }
    }
    
    private fun changeGridColumns(columns: Int) {
        if (columns in 2..5) {
            _uiState.value = _uiState.value.copy(gridColumns = columns)
        }
    }
}
