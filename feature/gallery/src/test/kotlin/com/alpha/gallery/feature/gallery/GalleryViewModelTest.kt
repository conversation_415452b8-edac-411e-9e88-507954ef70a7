package com.alpha.gallery.feature.gallery

import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.feature.gallery.model.GalleryUiEvent
import com.alpha.gallery.feature.gallery.model.GalleryUiAction
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
class GalleryViewModelTest {

    private val mediaRepository: MediaRepository = mock()
    private val permissionManager: PermissionManager = mock()

    @Test
    fun `openMediaItem should emit NavigateToMediaViewer action`() = runTest {
        // Given
        whenever(permissionManager.hasMediaPermissions()).thenReturn(true)
        whenever(mediaRepository.getAllMediaItems()).thenReturn(flowOf(emptyList()))
        
        val viewModel = GalleryViewModel(mediaRepository, permissionManager)
        
        // When
        viewModel.onEvent(GalleryUiEvent.OpenMediaItem("test-item-id"))
        
        // Then
        val action = viewModel.uiActions.first()
        assertTrue(action is GalleryUiAction.NavigateToMediaViewer)
        assertEquals("test-item-id", action.itemId)
    }
}
