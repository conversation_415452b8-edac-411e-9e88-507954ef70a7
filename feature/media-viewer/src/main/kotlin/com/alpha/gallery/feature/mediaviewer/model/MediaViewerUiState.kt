package com.alpha.gallery.feature.mediaviewer.model

import com.alpha.gallery.core.domain.model.MediaItem

/**
 * UI state for the Media Viewer screen
 */
data class MediaViewerUiState(
    val mediaItems: List<MediaItem> = emptyList(),
    val currentIndex: Int = 0,
    val isLoading: Boolean = false,
    val error: String? = null,
    val isInfoOverlayVisible: Boolean = false,
    val isControlsVisible: Boolean = true,
    val isZoomed: Boolean = false,
    val zoomScale: Float = 1f,
    val isVideoPlaying: Boolean = false,
    val videoPosition: Long = 0L,
    val videoDuration: Long = 0L,
    val isVideoControlsVisible: Boolean = true,
    val isFullscreen: Boolean = false,
    val hasPermissions: Boolean = false
) {
    val currentMediaItem: MediaItem?
        get() = mediaItems.getOrNull(currentIndex)
    
    val isCurrentItemVideo: Boolean
        get() = currentMediaItem?.isVideo == true
    
    val isCurrentItemImage: Boolean
        get() = currentMediaItem?.isVideo == false
    
    val canNavigatePrevious: Boole<PERSON>
        get() = currentIndex > 0
    
    val canNavigateNext: Boolean
        get() = currentIndex < mediaItems.size - 1
    
    val showLoadingState: Boolean
        get() = isLoading && mediaItems.isEmpty()
    
    val showContent: Boolean
        get() = mediaItems.isNotEmpty() && hasPermissions
    
    val showError: Boolean
        get() = error != null && !isLoading
    
    val videoProgress: Float
        get() = if (videoDuration > 0) videoPosition.toFloat() / videoDuration.toFloat() else 0f
}

/**
 * UI events for the Media Viewer screen
 */
sealed class MediaViewerUiEvent {
    object NavigateBack : MediaViewerUiEvent()
    object NavigatePrevious : MediaViewerUiEvent()
    object NavigateNext : MediaViewerUiEvent()
    object ToggleInfoOverlay : MediaViewerUiEvent()
    object ToggleControls : MediaViewerUiEvent()
    object ToggleFullscreen : MediaViewerUiEvent()
    object RetryLoad : MediaViewerUiEvent()
    object ClearError : MediaViewerUiEvent()
    
    // Image-specific events
    data class OnImageZoom(val scale: Float) : MediaViewerUiEvent()
    object ResetImageZoom : MediaViewerUiEvent()
    object ToggleImageFitMode : MediaViewerUiEvent()
    
    // Video-specific events
    object ToggleVideoPlayback : MediaViewerUiEvent()
    data class SeekVideo(val position: Long) : MediaViewerUiEvent()
    data class OnVideoPositionChanged(val position: Long) : MediaViewerUiEvent()
    data class OnVideoDurationChanged(val duration: Long) : MediaViewerUiEvent()
    object ToggleVideoControls : MediaViewerUiEvent()
    
    // Navigation events
    data class NavigateToIndex(val index: Int) : MediaViewerUiEvent()
    data class LoadMediaItems(val startingItemId: String) : MediaViewerUiEvent()
    
    // Gesture events
    object OnSingleTap : MediaViewerUiEvent()
    object OnDoubleTap : MediaViewerUiEvent()
    data class OnSwipeHorizontal(val direction: SwipeDirection) : MediaViewerUiEvent()
    data class OnSwipeVertical(val direction: SwipeDirection) : MediaViewerUiEvent()
}

/**
 * UI actions that need to be handled by the parent
 */
sealed class MediaViewerUiAction {
    object NavigateBack : MediaViewerUiAction()
    data class ShowMessage(val message: String) : MediaViewerUiAction()
    data class ShowError(val error: String) : MediaViewerUiAction()
    data class ShareMedia(val mediaItem: MediaItem) : MediaViewerUiAction()
    data class DeleteMedia(val mediaItem: MediaItem) : MediaViewerUiAction()
    data class EditMedia(val mediaItem: MediaItem) : MediaViewerUiAction()
    data class SetAsFavorite(val mediaItem: MediaItem, val isFavorite: Boolean) : MediaViewerUiAction()
}

/**
 * Swipe direction for gesture handling
 */
enum class SwipeDirection {
    LEFT, RIGHT, UP, DOWN
}

/**
 * Image fit mode for zoom behavior
 */
enum class ImageFitMode {
    FIT,    // Fit to screen (letterbox/pillarbox)
    FILL    // Fill screen (crop if necessary)
}

/**
 * Media viewer configuration
 */
data class MediaViewerConfig(
    val enableZoom: Boolean = true,
    val enableSwipeNavigation: Boolean = true,
    val enableInfoOverlay: Boolean = true,
    val autoHideControls: Boolean = true,
    val autoHideDelayMs: Long = 3000L,
    val maxZoomScale: Float = 5f,
    val minZoomScale: Float = 0.5f,
    val doubleTapZoomScale: Float = 2f,
    val enableSharedElementTransition: Boolean = true,
    val preloadAdjacentItems: Boolean = true,
    val preloadCount: Int = 2
)
