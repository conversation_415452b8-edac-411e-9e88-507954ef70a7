package com.alpha.gallery.feature.mediaviewer

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.alpha.gallery.core.domain.repository.MediaRepository
import com.alpha.gallery.core.sync.permission.PermissionManager
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiState
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiEvent
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerUiAction
import com.alpha.gallery.feature.mediaviewer.model.MediaViewerConfig
import com.alpha.gallery.feature.mediaviewer.model.SwipeDirection
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * ViewModel for the Media Viewer screen
 */
@HiltViewModel
class MediaViewerViewModel @Inject constructor(
    private val mediaRepository: MediaRepository,
    private val permissionManager: PermissionManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(MediaViewerUiState())
    val uiState: StateFlow<MediaViewerUiState> = _uiState.asStateFlow()
    
    private val _uiActions = MutableSharedFlow<MediaViewerUiAction>(
        replay = 0,
        extraBufferCapacity = 1
    )
    val uiActions: SharedFlow<MediaViewerUiAction> = _uiActions.asSharedFlow()
    
    private val config = MediaViewerConfig()
    private var autoHideControlsJob: kotlinx.coroutines.Job? = null
    
    init {
        // Check permissions
        _uiState.value = _uiState.value.copy(
            hasPermissions = permissionManager.hasMediaPermissions()
        )
    }
    
    /**
     * Handle UI events
     */
    fun onEvent(event: MediaViewerUiEvent) {
        when (event) {
            is MediaViewerUiEvent.LoadMediaItems -> loadMediaItems(event.startingItemId)
            is MediaViewerUiEvent.NavigateBack -> navigateBack()
            is MediaViewerUiEvent.NavigatePrevious -> navigatePrevious()
            is MediaViewerUiEvent.NavigateNext -> navigateNext()
            is MediaViewerUiEvent.NavigateToIndex -> navigateToIndex(event.index)
            is MediaViewerUiEvent.ToggleInfoOverlay -> toggleInfoOverlay()
            is MediaViewerUiEvent.ToggleControls -> toggleControls()
            is MediaViewerUiEvent.ToggleFullscreen -> toggleFullscreen()
            is MediaViewerUiEvent.RetryLoad -> retryLoad()
            is MediaViewerUiEvent.ClearError -> clearError()
            
            // Image events
            is MediaViewerUiEvent.OnImageZoom -> onImageZoom(event.scale)
            is MediaViewerUiEvent.ResetImageZoom -> resetImageZoom()
            is MediaViewerUiEvent.ToggleImageFitMode -> toggleImageFitMode()
            
            // Video events
            is MediaViewerUiEvent.ToggleVideoPlayback -> toggleVideoPlayback()
            is MediaViewerUiEvent.SeekVideo -> seekVideo(event.position)
            is MediaViewerUiEvent.OnVideoPositionChanged -> onVideoPositionChanged(event.position)
            is MediaViewerUiEvent.OnVideoDurationChanged -> onVideoDurationChanged(event.duration)
            is MediaViewerUiEvent.ToggleVideoControls -> toggleVideoControls()
            
            // Gesture events
            is MediaViewerUiEvent.OnSingleTap -> onSingleTap()
            is MediaViewerUiEvent.OnDoubleTap -> onDoubleTap()
            is MediaViewerUiEvent.OnSwipeHorizontal -> onSwipeHorizontal(event.direction)
            is MediaViewerUiEvent.OnSwipeVertical -> onSwipeVertical(event.direction)
        }
    }
    
    private fun loadMediaItems(startingItemId: String) {
        if (!permissionManager.hasMediaPermissions()) {
            _uiState.value = _uiState.value.copy(
                error = "Media permissions required to view media"
            )
            return
        }
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                mediaRepository.getAllMediaItems()
                    .catch { e ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = e.message ?: "Failed to load media items"
                        )
                    }
                    .collect { mediaItems ->
                        val startingIndex = mediaItems.indexOfFirst { it.id == startingItemId }
                        val validIndex = if (startingIndex >= 0) startingIndex else 0
                        
                        _uiState.value = _uiState.value.copy(
                            mediaItems = mediaItems,
                            currentIndex = validIndex,
                            isLoading = false,
                            error = null
                        )
                        
                        // Start auto-hide timer for controls
                        startAutoHideControlsTimer()
                    }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Failed to load media items"
                )
            }
        }
    }
    
    private fun navigateBack() {
        _uiActions.tryEmit(MediaViewerUiAction.NavigateBack)
    }
    
    private fun navigatePrevious() {
        val currentState = _uiState.value
        if (currentState.canNavigatePrevious) {
            _uiState.value = currentState.copy(
                currentIndex = currentState.currentIndex - 1,
                isZoomed = false,
                zoomScale = 1f,
                isVideoPlaying = false,
                videoPosition = 0L
            )
            resetAutoHideTimer()
        }
    }
    
    private fun navigateNext() {
        val currentState = _uiState.value
        if (currentState.canNavigateNext) {
            _uiState.value = currentState.copy(
                currentIndex = currentState.currentIndex + 1,
                isZoomed = false,
                zoomScale = 1f,
                isVideoPlaying = false,
                videoPosition = 0L
            )
            resetAutoHideTimer()
        }
    }
    
    private fun navigateToIndex(index: Int) {
        val currentState = _uiState.value
        if (index in 0 until currentState.mediaItems.size) {
            _uiState.value = currentState.copy(
                currentIndex = index,
                isZoomed = false,
                zoomScale = 1f,
                isVideoPlaying = false,
                videoPosition = 0L
            )
            resetAutoHideTimer()
        }
    }
    
    private fun toggleInfoOverlay() {
        _uiState.value = _uiState.value.copy(
            isInfoOverlayVisible = !_uiState.value.isInfoOverlayVisible
        )
        resetAutoHideTimer()
    }
    
    private fun toggleControls() {
        _uiState.value = _uiState.value.copy(
            isControlsVisible = !_uiState.value.isControlsVisible
        )
        if (_uiState.value.isControlsVisible) {
            startAutoHideControlsTimer()
        }
    }
    
    private fun toggleFullscreen() {
        _uiState.value = _uiState.value.copy(
            isFullscreen = !_uiState.value.isFullscreen
        )
    }
    
    private fun retryLoad() {
        // Retry with the current starting item if available
        val currentItem = _uiState.value.currentMediaItem
        if (currentItem != null) {
            loadMediaItems(currentItem.id)
        }
    }
    
    private fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    // Image-specific methods
    private fun onImageZoom(scale: Float) {
        _uiState.value = _uiState.value.copy(
            zoomScale = scale.coerceIn(config.minZoomScale, config.maxZoomScale),
            isZoomed = scale > 1f
        )
        resetAutoHideTimer()
    }
    
    private fun resetImageZoom() {
        _uiState.value = _uiState.value.copy(
            zoomScale = 1f,
            isZoomed = false
        )
    }
    
    private fun toggleImageFitMode() {
        // This would toggle between fit and fill modes
        // Implementation depends on the image component
        resetAutoHideTimer()
    }
    
    // Video-specific methods
    private fun toggleVideoPlayback() {
        _uiState.value = _uiState.value.copy(
            isVideoPlaying = !_uiState.value.isVideoPlaying
        )
        resetAutoHideTimer()
    }
    
    private fun seekVideo(position: Long) {
        _uiState.value = _uiState.value.copy(videoPosition = position)
        resetAutoHideTimer()
    }
    
    private fun onVideoPositionChanged(position: Long) {
        _uiState.value = _uiState.value.copy(videoPosition = position)
    }
    
    private fun onVideoDurationChanged(duration: Long) {
        _uiState.value = _uiState.value.copy(videoDuration = duration)
    }
    
    private fun toggleVideoControls() {
        _uiState.value = _uiState.value.copy(
            isVideoControlsVisible = !_uiState.value.isVideoControlsVisible
        )
        if (_uiState.value.isVideoControlsVisible) {
            startAutoHideControlsTimer()
        }
    }
    
    // Gesture handling
    private fun onSingleTap() {
        toggleControls()
    }
    
    private fun onDoubleTap() {
        if (_uiState.value.isCurrentItemImage) {
            val currentScale = _uiState.value.zoomScale
            val targetScale = if (currentScale > 1f) 1f else config.doubleTapZoomScale
            onImageZoom(targetScale)
        } else if (_uiState.value.isCurrentItemVideo) {
            toggleVideoPlayback()
        }
    }
    
    private fun onSwipeHorizontal(direction: SwipeDirection) {
        if (!config.enableSwipeNavigation || _uiState.value.isZoomed) return
        
        when (direction) {
            SwipeDirection.LEFT -> navigateNext()
            SwipeDirection.RIGHT -> navigatePrevious()
            else -> {}
        }
    }
    
    private fun onSwipeVertical(direction: SwipeDirection) {
        when (direction) {
            SwipeDirection.DOWN -> {
                if (!_uiState.value.isZoomed) {
                    navigateBack()
                }
            }
            SwipeDirection.UP -> toggleInfoOverlay()
            else -> {}
        }
    }
    
    // Auto-hide controls timer
    private fun startAutoHideControlsTimer() {
        if (!config.autoHideControls) return
        
        autoHideControlsJob?.cancel()
        autoHideControlsJob = viewModelScope.launch {
            delay(config.autoHideDelayMs)
            if (_uiState.value.isControlsVisible) {
                _uiState.value = _uiState.value.copy(isControlsVisible = false)
            }
        }
    }
    
    private fun resetAutoHideTimer() {
        if (_uiState.value.isControlsVisible) {
            startAutoHideControlsTimer()
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        autoHideControlsJob?.cancel()
    }
}
