package com.alpha.gallery.feature.mediaviewer.util

import androidx.compose.animation.core.*
import androidx.compose.foundation.gestures.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.*
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlin.math.*

/**
 * Enhanced gesture detection for media viewer
 */
@Composable
fun Modifier.mediaViewerGestures(
    onSingleTap: (Offset) -> Unit = {},
    onDoubleTap: (Offset) -> Unit = {},
    onLongPress: (Offset) -> Unit = {},
    onSwipe: (SwipeDirection, Float) -> Unit = { _, _ -> },
    onZoom: (Float, Offset) -> Unit = { _, _ -> },
    onPan: (Offset) -> Unit = {},
    enabled: Boolean = true
): Modifier {
    if (!enabled) return this
    
    val density = LocalDensity.current
    
    return this.pointerInput(Unit) {
        detectTapGestures(
            onTap = onSingleTap,
            onDoubleTap = onDoubleTap,
            onLongPress = onLongPress
        )
    }.pointerInput(Unit) {
        detectTransformGestures { centroid, pan, zoom, _ ->
            if (zoom != 1f) {
                onZoom(zoom, centroid)
            } else if (pan != Offset.Zero) {
                onPan(pan)
            }
        }
    }.pointerInput(Unit) {
        detectDragGestures(
            onDragStart = { },
            onDragEnd = { },
            onDrag = { change, dragAmount ->
                // Detect swipe gestures
                val swipeThreshold = with(density) { 50.dp.toPx() }
                val velocity = change.position - change.previousPosition
                
                if (abs(dragAmount.x) > swipeThreshold || abs(dragAmount.y) > swipeThreshold) {
                    val direction = when {
                        abs(dragAmount.x) > abs(dragAmount.y) -> {
                            if (dragAmount.x > 0) SwipeDirection.RIGHT else SwipeDirection.LEFT
                        }
                        else -> {
                            if (dragAmount.y > 0) SwipeDirection.DOWN else SwipeDirection.UP
                        }
                    }
                    onSwipe(direction, maxOf(abs(dragAmount.x), abs(dragAmount.y)))
                }
            }
        )
    }
}

/**
 * Swipe direction enum
 */
enum class SwipeDirection {
    LEFT, RIGHT, UP, DOWN
}

/**
 * Calculate zoom bounds for an image
 */
fun calculateZoomBounds(
    imageWidth: Float,
    imageHeight: Float,
    containerWidth: Float,
    containerHeight: Float,
    currentScale: Float
): Pair<Offset, Offset> {
    val scaledImageWidth = imageWidth * currentScale
    val scaledImageHeight = imageHeight * currentScale
    
    val maxOffsetX = maxOf(0f, (scaledImageWidth - containerWidth) / 2f)
    val maxOffsetY = maxOf(0f, (scaledImageHeight - containerHeight) / 2f)
    
    return Offset(-maxOffsetX, -maxOffsetY) to Offset(maxOffsetX, maxOffsetY)
}

/**
 * Constrain offset within bounds
 */
fun constrainOffset(
    offset: Offset,
    minBounds: Offset,
    maxBounds: Offset
): Offset {
    return Offset(
        x = offset.x.coerceIn(minBounds.x, maxBounds.x),
        y = offset.y.coerceIn(minBounds.y, maxBounds.y)
    )
}

/**
 * Calculate the optimal scale for fitting content
 */
fun calculateFitScale(
    contentWidth: Float,
    contentHeight: Float,
    containerWidth: Float,
    containerHeight: Float
): Float {
    val scaleX = containerWidth / contentWidth
    val scaleY = containerHeight / contentHeight
    return minOf(scaleX, scaleY)
}

/**
 * Calculate the scale for filling the container
 */
fun calculateFillScale(
    contentWidth: Float,
    contentHeight: Float,
    containerWidth: Float,
    containerHeight: Float
): Float {
    val scaleX = containerWidth / contentWidth
    val scaleY = containerHeight / contentHeight
    return maxOf(scaleX, scaleY)
}

/**
 * Animate to target values with spring animation
 */
@Composable
fun animateToTarget(
    targetScale: Float,
    targetOffset: Offset,
    currentScale: Animatable<Float, AnimationVector1D>,
    currentOffsetX: Animatable<Float, AnimationVector1D>,
    currentOffsetY: Animatable<Float, AnimationVector1D>,
    animationSpec: AnimationSpec<Float> = spring(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessLow
    )
) {
    LaunchedEffect(targetScale, targetOffset) {
        coroutineScope {
            launch {
                currentScale.animateTo(targetScale, animationSpec)
            }
            launch {
                currentOffsetX.animateTo(targetOffset.x, animationSpec)
            }
            launch {
                currentOffsetY.animateTo(targetOffset.y, animationSpec)
            }
        }
    }
}

/**
 * Format file size in human readable format
 */
fun formatFileSize(bytes: Long): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = bytes.toDouble()
    var unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }
    
    return if (unitIndex == 0) {
        "${size.toInt()} ${units[unitIndex]}"
    } else {
        "%.1f %s".format(size, units[unitIndex])
    }
}

/**
 * Format duration in human readable format
 */
fun formatDuration(durationMs: Long): String {
    val totalSeconds = durationMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60
    
    return when {
        hours > 0 -> "%d:%02d:%02d".format(hours, minutes, seconds)
        else -> "%d:%02d".format(minutes, seconds)
    }
}

/**
 * Check if a point is within bounds
 */
fun isWithinBounds(
    point: Offset,
    bounds: androidx.compose.ui.geometry.Rect
): Boolean {
    return point.x >= bounds.left &&
            point.x <= bounds.right &&
            point.y >= bounds.top &&
            point.y <= bounds.bottom
}

/**
 * Calculate distance between two points
 */
fun distance(point1: Offset, point2: Offset): Float {
    val dx = point1.x - point2.x
    val dy = point1.y - point2.y
    return sqrt(dx * dx + dy * dy)
}

/**
 * Lerp between two offsets
 */
fun lerpOffset(start: Offset, end: Offset, fraction: Float): Offset {
    return Offset(
        x = start.x + (end.x - start.x) * fraction,
        y = start.y + (end.y - start.y) * fraction
    )
}

/**
 * Extension function to get aspect ratio
 */
val androidx.compose.ui.geometry.Size.aspectRatio: Float
    get() = if (height > 0) width / height else 1f

/**
 * Extension function to check if size is valid
 */
val androidx.compose.ui.geometry.Size.isValid: Boolean
    get() = width > 0 && height > 0

/**
 * Extension function to scale size
 */
fun androidx.compose.ui.geometry.Size.scale(factor: Float): androidx.compose.ui.geometry.Size {
    return androidx.compose.ui.geometry.Size(width * factor, height * factor)
}

/**
 * Extension function to fit size within bounds
 */
fun androidx.compose.ui.geometry.Size.fitWithin(bounds: androidx.compose.ui.geometry.Size): androidx.compose.ui.geometry.Size {
    val scale = calculateFitScale(width, height, bounds.width, bounds.height)
    return scale(scale)
}

/**
 * Extension function to fill bounds
 */
fun androidx.compose.ui.geometry.Size.fillBounds(bounds: androidx.compose.ui.geometry.Size): androidx.compose.ui.geometry.Size {
    val scale = calculateFillScale(width, height, bounds.width, bounds.height)
    return scale(scale)
}
