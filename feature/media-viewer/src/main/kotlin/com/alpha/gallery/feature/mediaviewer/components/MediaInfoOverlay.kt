package com.alpha.gallery.feature.mediaviewer.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.alpha.gallery.core.domain.model.MediaItem
import java.text.SimpleDateFormat
import java.util.*

/**
 * Media info overlay showing detailed metadata
 */
@Composable
fun MediaInfoOverlay(
    mediaItem: MediaItem?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (mediaItem == null) return
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
            .clickable { onDismiss() },
        color = Color.Black.copy(alpha = 0.9f)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // Header with dismiss handle
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                Surface(
                    modifier = Modifier
                        .width(40.dp)
                        .height(4.dp)
                        .clip(RoundedCornerShape(2.dp)),
                    color = Color.White.copy(alpha = 0.3f)
                ) {}
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Media name
            Text(
                text = mediaItem.name,
                color = Color.White,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Basic info grid
            MediaInfoGrid(mediaItem = mediaItem)
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Detailed info sections
            MediaInfoSection(
                title = "File Details",
                items = getFileDetails(mediaItem)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            MediaInfoSection(
                title = "Media Properties",
                items = getMediaProperties(mediaItem)
            )
            
            if (mediaItem.albumName != null) {
                Spacer(modifier = Modifier.height(16.dp))
                
                MediaInfoSection(
                    title = "Album",
                    items = listOf(
                        InfoItem(
                            icon = Icons.Default.Info,
                            label = "Album",
                            value = mediaItem.albumName ?: ""
                        )
                    )
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

/**
 * Grid layout for basic media information
 */
@Composable
fun MediaInfoGrid(
    mediaItem: MediaItem,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        // File size
        MediaInfoCard(
            icon = Icons.Default.Info,
            label = "Size",
            value = formatFileSize(mediaItem.size),
            modifier = Modifier.weight(1f)
        )

        Spacer(modifier = Modifier.width(12.dp))

        // Dimensions
        MediaInfoCard(
            icon = Icons.Default.Info,
            label = "Dimensions",
            value = "${mediaItem.width} × ${mediaItem.height}",
            modifier = Modifier.weight(1f)
        )

        if (mediaItem.isVideo && mediaItem.duration > 0) {
            Spacer(modifier = Modifier.width(12.dp))

            // Duration for videos
            MediaInfoCard(
                icon = Icons.Default.PlayArrow,
                label = "Duration",
                value = formatDuration(mediaItem.duration),
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * Individual info card in the grid
 */
@Composable
fun MediaInfoCard(
    icon: ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier,
        color = Color.White.copy(alpha = 0.1f),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = value,
                color = Color.White,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = label,
                color = Color.White.copy(alpha = 0.7f),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

/**
 * Section with multiple info items
 */
@Composable
fun MediaInfoSection(
    title: String,
    items: List<InfoItem>,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            color = Color.White,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        items.forEach { item ->
            MediaInfoRow(
                icon = item.icon,
                label = item.label,
                value = item.value
            )
            
            if (item != items.last()) {
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

/**
 * Individual info row
 */
@Composable
fun MediaInfoRow(
    icon: ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = Color.White.copy(alpha = 0.7f),
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = label,
            color = Color.White.copy(alpha = 0.7f),
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.width(100.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Text(
            text = value,
            color = Color.White,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * Data class for info items
 */
data class InfoItem(
    val icon: ImageVector,
    val label: String,
    val value: String
)

/**
 * Get file details for the media item
 */
private fun getFileDetails(mediaItem: MediaItem): List<InfoItem> {
    return listOf(
        InfoItem(
            icon = Icons.Default.Info,
            label = "Name",
            value = mediaItem.name
        ),
        InfoItem(
            icon = Icons.Default.Info,
            label = "Path",
            value = mediaItem.path
        ),
        InfoItem(
            icon = Icons.Default.Info,
            label = "Type",
            value = mediaItem.mimeType
        ),
        InfoItem(
            icon = Icons.Default.Info,
            label = "Size",
            value = formatFileSize(mediaItem.size)
        ),
        InfoItem(
            icon = Icons.Default.DateRange,
            label = "Modified",
            value = formatDate(mediaItem.dateModified)
        ),
        InfoItem(
            icon = Icons.Default.Add,
            label = "Added",
            value = formatDate(mediaItem.dateAdded)
        )
    )
}

/**
 * Get media properties for the media item
 */
private fun getMediaProperties(mediaItem: MediaItem): List<InfoItem> {
    val items = mutableListOf<InfoItem>()

    items.add(
        InfoItem(
            icon = Icons.Default.Info,
            label = "Dimensions",
            value = "${mediaItem.width} × ${mediaItem.height}"
        )
    )

    if (mediaItem.aspectRatio > 0) {
        items.add(
            InfoItem(
                icon = Icons.Default.Info,
                label = "Aspect Ratio",
                value = String.format("%.2f:1", mediaItem.aspectRatio)
            )
        )
    }

    if (mediaItem.isVideo && mediaItem.duration > 0) {
        items.add(
            InfoItem(
                icon = Icons.Default.PlayArrow,
                label = "Duration",
                value = formatDuration(mediaItem.duration)
            )
        )
    }

    return items
}

/**
 * Format file size in human readable format
 */
private fun formatFileSize(bytes: Long): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = bytes.toDouble()
    var unitIndex = 0

    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }

    return if (unitIndex == 0) {
        "${size.toInt()} ${units[unitIndex]}"
    } else {
        "%.1f %s".format(size, units[unitIndex])
    }
}

/**
 * Format duration in human readable format
 */
private fun formatDuration(durationMs: Long): String {
    val totalSeconds = durationMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60

    return when {
        hours > 0 -> "%d:%02d:%02d".format(hours, minutes, seconds)
        else -> "%d:%02d".format(minutes, seconds)
    }
}

/**
 * Format date in human readable format
 */
private fun formatDate(timestamp: Long): String {
    val date = Date(timestamp)
    val formatter = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault())
    return formatter.format(date)
}
