package com.alpha.gallery.feature.mediaviewer.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.alpha.gallery.core.domain.model.MediaItem
import kotlinx.coroutines.launch
import kotlin.math.*

/**
 * Zoomable image component with iOS-like interactions
 */
@Composable
fun ZoomableImage(
    mediaItem: MediaItem,
    modifier: Modifier = Modifier,
    onSingleTap: () -> Unit = {},
    onDoubleTap: () -> Unit = {},
    onZoomChange: (Float) -> Unit = {},
    minScale: Float = 0.5f,
    maxScale: Float = 5f,
    doubleTapScale: Float = 2f,
    contentScale: ContentScale = ContentScale.Fit
) {
    val context = LocalContext.current
    val density = LocalDensity.current
    val coroutineScope = rememberCoroutineScope()
    
    // Zoom and pan state
    var scale by remember { mutableFloatStateOf(1f) }
    var offsetX by remember { mutableFloatStateOf(0f) }
    var offsetY by remember { mutableFloatStateOf(0f) }
    
    // Animation states
    val animatedScale = remember { Animatable(1f) }
    val animatedOffsetX = remember { Animatable(0f) }
    val animatedOffsetY = remember { Animatable(0f) }
    
    // Image dimensions
    var imageSize by remember { mutableStateOf(IntSize.Zero) }
    var containerSize by remember { mutableStateOf(IntSize.Zero) }
    
    // Reset zoom when media item changes
    LaunchedEffect(mediaItem.id) {
        scale = 1f
        offsetX = 0f
        offsetY = 0f
        animatedScale.snapTo(1f)
        animatedOffsetX.snapTo(0f)
        animatedOffsetY.snapTo(0f)
    }
    
    // Sync animated values with state
    LaunchedEffect(animatedScale.value, animatedOffsetX.value, animatedOffsetY.value) {
        scale = animatedScale.value
        offsetX = animatedOffsetX.value
        offsetY = animatedOffsetY.value
        onZoomChange(scale)
    }
    
    // Calculate bounds for panning
    fun calculateBounds(): Pair<Offset, Offset> {
        if (containerSize == IntSize.Zero || imageSize == IntSize.Zero) {
            return Offset.Zero to Offset.Zero
        }
        
        val scaledImageWidth = imageSize.width * scale
        val scaledImageHeight = imageSize.height * scale
        
        val maxOffsetX = maxOf(0f, (scaledImageWidth - containerSize.width) / 2f)
        val maxOffsetY = maxOf(0f, (scaledImageHeight - containerSize.height) / 2f)
        
        return Offset(-maxOffsetX, -maxOffsetY) to Offset(maxOffsetX, maxOffsetY)
    }
    
    // Constrain offset to bounds
    fun constrainOffset(offset: Offset): Offset {
        val (minBounds, maxBounds) = calculateBounds()
        return Offset(
            x = offset.x.coerceIn(minBounds.x, maxBounds.x),
            y = offset.y.coerceIn(minBounds.y, maxBounds.y)
        )
    }
    
    // Animate to target values
    fun animateToTarget(
        targetScale: Float,
        targetOffset: Offset = Offset(offsetX, offsetY),
        animationSpec: AnimationSpec<Float> = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        )
    ) {
        val constrainedOffset = constrainOffset(targetOffset)
        
        coroutineScope.launch {
            launch {
                animatedScale.animateTo(
                    targetValue = targetScale.coerceIn(minScale, maxScale),
                    animationSpec = animationSpec
                )
            }
            launch {
                animatedOffsetX.animateTo(
                    targetValue = constrainedOffset.x,
                    animationSpec = animationSpec
                )
            }
            launch {
                animatedOffsetY.animateTo(
                    targetValue = constrainedOffset.y,
                    animationSpec = animationSpec
                )
            }
        }
    }
    
    // Handle double tap to zoom
    fun handleDoubleTap(tapOffset: Offset) {
        val targetScale = if (scale > 1f) 1f else doubleTapScale
        
        if (targetScale == 1f) {
            // Zoom out to center
            animateToTarget(targetScale, Offset.Zero)
        } else {
            // Zoom in to tap location
            val containerCenter = Offset(
                containerSize.width / 2f,
                containerSize.height / 2f
            )
            val targetOffset = (containerCenter - tapOffset) * (targetScale - 1f)
            animateToTarget(targetScale, targetOffset)
        }
        
        onDoubleTap()
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { onSingleTap() },
                    onDoubleTap = { offset -> handleDoubleTap(offset) }
                )
            }
            .pointerInput(Unit) {
                detectTransformGestures(
                    panZoomLock = true
                ) { centroid, pan, zoom, _ ->
                    val newScale = (scale * zoom).coerceIn(minScale, maxScale)
                    val newOffset = if (newScale > minScale) {
                        constrainOffset(Offset(offsetX + pan.x, offsetY + pan.y))
                    } else {
                        Offset.Zero
                    }
                    
                    // Update animated values directly for smooth interaction
                    coroutineScope.launch {
                        animatedScale.snapTo(newScale)
                        animatedOffsetX.snapTo(newOffset.x)
                        animatedOffsetY.snapTo(newOffset.y)
                    }
                }
            }
            .onGloballyPositioned { coordinates ->
                containerSize = coordinates.size
            }
    ) {
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(mediaItem.uri)
                .crossfade(true)
                .build(),
            contentDescription = mediaItem.name,
            contentScale = contentScale,
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer(
                    scaleX = scale,
                    scaleY = scale,
                    translationX = offsetX,
                    translationY = offsetY
                )
                .onGloballyPositioned { coordinates ->
                    imageSize = coordinates.size
                }
        )
        
        // Loading indicator
        if (imageSize == IntSize.Zero) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(48.dp),
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * Image fit mode toggle button
 */
@Composable
fun ImageFitModeButton(
    contentScale: ContentScale,
    onToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onToggle,
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.5f),
                RoundedCornerShape(20.dp)
            )
    ) {
        Icon(
            imageVector = when (contentScale) {
                ContentScale.Fit -> Icons.Default.Add
                ContentScale.Crop -> Icons.Default.Close
                else -> Icons.Default.Add
            },
            contentDescription = when (contentScale) {
                ContentScale.Fit -> "Fill screen"
                ContentScale.Crop -> "Fit to screen"
                else -> "Toggle fit mode"
            },
            tint = Color.White
        )
    }
}

/**
 * Zoom level indicator
 */
@Composable
fun ZoomLevelIndicator(
    zoomLevel: Float,
    visible: Boolean,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = visible && zoomLevel != 1f,
        enter = fadeIn() + scaleIn(),
        exit = fadeOut() + scaleOut(),
        modifier = modifier
    ) {
        Surface(
            modifier = Modifier
                .clip(RoundedCornerShape(16.dp)),
            color = Color.Black.copy(alpha = 0.7f)
        ) {
            Text(
                text = "${(zoomLevel * 100).roundToInt()}%",
                color = Color.White,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
            )
        }
    }
}
